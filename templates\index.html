<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>EchoVerse - Professional Text to Speech</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Google Fonts for professional look -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Professional dark background */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #e2e8f0;
            line-height: 1.6;
        }
        .container {
            background: rgba(30, 41, 59, 0.95);
            border: 1px solid rgba(71, 85, 105, 0.3);
            border-radius: 16px;
            box-shadow: 
                0 20px 25px -5px rgba(0, 0, 0, 0.1),
                0 10px 10px -5px rgba(0, 0, 0, 0.04);
            padding: 48px;
            max-width: 800px;
            width: 90%;
            margin: 40px auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            animation: fadeIn 0.6s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px);}
            to { opacity: 1; transform: translateY(0);}
        }
        h2 {
            color: #f8fafc;
            font-weight: 600;
            font-size: 2.5rem;
            margin-bottom: 32px;
            letter-spacing: -0.025em;
            text-align: center;
        }
        .tone-row {
            display: flex;
            gap: 16px;
            margin-bottom: 32px;
            width: 100%;
            justify-content: center;
            flex-wrap: wrap;
        }
        .tone-btn {
            background: rgba(51, 65, 85, 0.8);
            border: 1px solid rgba(71, 85, 105, 0.4);
            color: #cbd5e1;
            font-weight: 500;
            border-radius: 8px;
            padding: 12px 24px;
            cursor: pointer;
            font-size: 0.95rem;
            transition: all 0.2s ease;
            outline: none;
        }
        
        .tone-btn.active, .tone-btn:focus {
            background: rgba(59, 130, 246, 0.9);
            border-color: rgba(59, 130, 246, 0.6);
            color: #ffffff;
            box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.25);
        }
        
        .tone-btn:hover {
            background: rgba(71, 85, 105, 0.8);
            border-color: rgba(100, 116, 139, 0.6);
            transform: translateY(-1px);
        }
        .input-area {
            width: 100%;
            margin-bottom: 32px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        textarea {
            width: 100%;
            min-height: 140px;
            font-size: 1rem;
            margin-bottom: 16px;
            border-radius: 8px;
            border: 1px solid rgba(71, 85, 105, 0.4);
            padding: 16px;
            background: rgba(30, 41, 59, 0.8);
            color: #e2e8f0;
            transition: all 0.2s ease;
            resize: vertical;
            font-family: inherit;
            line-height: 1.5;
        }
        
        textarea:focus {
            border-color: rgba(59, 130, 246, 0.6);
            outline: none;
            background: rgba(30, 41, 59, 0.95);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        textarea::placeholder {
            color: #94a3b8;
        }
        /* Enhanced button styles */
        .btn {
            background: rgba(59, 130, 246, 0.9);
            color: #ffffff;
            border: 1px solid rgba(59, 130, 246, 0.6);
            padding: 12px 32px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            outline: none;
            font-family: inherit;
        }
        
        .btn:hover {
            background: rgba(59, 130, 246, 1);
            transform: translateY(-1px);
            box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.25);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        .file-upload {
            width: 100%;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }
        .file-upload input[type="file"] {
            display: none;
        }
        .file-upload-btn {
            background: rgba(71, 85, 105, 0.8);
            color: #cbd5e1;
            border: 1px solid rgba(100, 116, 139, 0.4);
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            font-family: inherit;
        }
        
        .file-upload-btn:hover, .file-upload-btn:focus {
            background: rgba(100, 116, 139, 0.8);
            border-color: rgba(148, 163, 184, 0.6);
            transform: translateY(-1px);
        }
        #fileName {
            color: #94a3b8;
            font-size: 0.9rem;
        }
        #generateBtn {
            width: 100%;
            background: rgba(59, 130, 246, 0.9);
            color: #ffffff;
            border: 1px solid rgba(59, 130, 246, 0.6);
            padding: 16px 0;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 24px;
            transition: all 0.2s ease;
            outline: none;
            font-family: inherit;
        }
        
        #generateBtn:active {
            transform: translateY(0);
        }
        
        #generateBtn:hover, #generateBtn:focus {
            background: rgba(59, 130, 246, 1);
            transform: translateY(-1px);
            box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.25);
        }
        
        #generateBtn:disabled {
            background: rgba(71, 85, 105, 0.5);
            border-color: rgba(71, 85, 105, 0.3);
            color: #94a3b8;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        #status {
            color: #cbd5e1;
            margin-bottom: 16px;
            min-height: 24px;
            text-align: center;
            font-size: 0.95rem;
        }
        .audio-row {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
        }
        audio {
            width: 100%;
            border-radius: 8px;
            background: rgba(30, 41, 59, 0.8);
            outline: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        #downloadBtn {
            background: rgba(34, 197, 94, 0.9);
            color: #ffffff;
            border: 1px solid rgba(34, 197, 94, 0.6);
            padding: 10px 24px;
            border-radius: 8px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            outline: none;
            font-family: inherit;
        }
        
        #downloadBtn:hover, #downloadBtn:focus {
            background: rgba(34, 197, 94, 1);
            transform: translateY(-1px);
            box-shadow: 0 4px 14px 0 rgba(34, 197, 94, 0.25);
        }
        
        #downloadBtn:active {
            transform: translateY(0);
        }
        
        @media (max-width: 768px) {
            .container { 
                padding: 32px 24px; 
                margin: 20px auto;
                width: 95%;
            }
            h2 { font-size: 2rem; }
            .tone-row { flex-direction: column; gap: 12px; }
            .tone-btn { width: 100%; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>EchoVerse</h2>
        <!-- Tone selection buttons -->
        <div class="tone-row" id="toneRow">
            <button class="tone-btn active" data-tone="neutral" id="toneNeutral">Neutral</button>
            <button class="tone-btn" data-tone="suspense" id="toneSuspense">Suspense</button>
            <button class="tone-btn" data-tone="motivational" id="toneMotivational">Motivational</button>
        </div>
        <!-- Input area: textarea or file upload -->
        <div class="input-area">
            <div class="file-upload">
                <label class="file-upload-btn" for="fileInput">Upload .txt</label>
                <input type="file" id="fileInput" accept=".txt">
                <span id="fileName"></span>
            </div>
            <textarea id="story" placeholder="Type or paste your story here..."></textarea>
        </div>
        <div id="status"></div>
        <button id="generateBtn">Generate</button>
        <div class="audio-row">
            <audio id="audioPlayer" controls style="display:none;"></audio>
            <button id="downloadBtn" style="display:none;">Download Audio</button>
        </div>
    </div>
    <script>
        // --- Tone selection logic with animation ---
        // Keeps track of the selected tone and updates button styles
        const toneRow = document.getElementById('toneRow');
        let selectedTone = 'neutral'; // Default

        toneRow.addEventListener('click', function(e) {
            if (e.target.classList.contains('tone-btn')) {
                // Remove 'active' from all buttons
                document.querySelectorAll('.tone-btn').forEach(btn => btn.classList.remove('active'));
                // Add 'active' to clicked button
                e.target.classList.add('active');
                selectedTone = e.target.getAttribute('data-tone');
            }
        });

        // --- File upload logic for .txt files ---
        const fileInput = document.getElementById('fileInput');
        const fileNameSpan = document.getElementById('fileName');
        const textarea = document.getElementById('story');

        // When a file is selected, read its contents into the textarea
        fileInput.addEventListener('change', function() {
            const file = fileInput.files[0];
            if (file && file.type === "text/plain") {
                fileNameSpan.textContent = file.name;
                const reader = new FileReader();
                reader.onload = function(e) {
                    textarea.value = e.target.result;
                };
                reader.readAsText(file);
            } else {
                fileNameSpan.textContent = '';
                textarea.value = '';
            }
        });


        
        // --- Generate button logic (AI narration logic untouched) ---
        document.getElementById('generateBtn').onclick = async function(e) {
            e.preventDefault();
            const btn = this;
            const audio = document.getElementById('audioPlayer');
            const status = document.getElementById('status');
            const downloadBtn = document.getElementById('downloadBtn');
            const text = textarea.value.trim();
            if (!text) {
                status.textContent = 'Please enter some text or upload a .txt file.';
                return;
            }
            btn.disabled = true;
            status.textContent = "Generating audio...";
            downloadBtn.style.display = "none";
            try {
                const formData = new FormData();
                formData.append('text', text);
                // Optionally send tone to backend in the future:
                // formData.append('tone', selectedTone);
                const response = await fetch('/speak', { method: 'POST', body: formData });
                const data = await response.json();
                if (data.success || data.audio_url) {
                    // Support both {success:true, file:...} and {audio_url:...}
                    const audioUrl = data.file || data.audio_url;
                    audio.src = audioUrl;
                    audio.style.display = '';
                    audio.load();
                    audio.play();
                    status.textContent = "Audio ready!";
                    textarea.value = "";
                    // Show download button and set its link
                    downloadBtn.style.display = '';
                    downloadBtn.onclick = function() {
                        // Download the audio file
                        const link = document.createElement('a');
                        link.href = audioUrl;
                        link.download = audioUrl.split('/').pop();
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                    };
                } else {
                    status.textContent = data.error || "Failed to generate audio.";
                }
            } catch (err) {
                status.textContent = "Error contacting server.";
            } finally {
                btn.disabled = false;
            }
        };

        // Focus textarea on load for fast typing
        textarea.focus();
    </script>
</body>
</html>