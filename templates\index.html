<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>EchoVerse - Professional Text to Speech</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Google Fonts for professional look -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Enhanced professional dark background with subtle patterns */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.05) 0%, transparent 50%),
                linear-gradient(135deg, #0a0a1a 0%, #1a1a2e 30%, #16213e 70%, #0f1419 100%);
            color: #e2e8f0;
            line-height: 1.6;
            position: relative;
            overflow-x: hidden;
        }

        /* Subtle animated background elements */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(168, 85, 247, 0.02) 0%, transparent 50%);
            animation: subtleFloat 20s ease-in-out infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes subtleFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(1deg); }
        }

        .container {
            background: rgba(15, 23, 42, 0.85);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(71, 85, 105, 0.2);
            border-radius: 24px;
            box-shadow:
                0 25px 50px -12px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            padding: 48px;
            max-width: 850px;
            width: 90%;
            margin: 40px auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        /* Enhanced container glow effect */
        .container::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg,
                rgba(99, 102, 241, 0.1),
                rgba(168, 85, 247, 0.1),
                rgba(59, 130, 246, 0.1));
            border-radius: 24px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .container:hover::before {
            opacity: 1;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        h2 {
            color: #f8fafc;
            font-weight: 700;
            font-size: 2.75rem;
            margin-bottom: 40px;
            letter-spacing: -0.025em;
            text-align: center;
            background: linear-gradient(135deg, #f8fafc 0%, #cbd5e1 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            animation: titleGlow 3s ease-in-out infinite alternate;
        }

        @keyframes titleGlow {
            0% { filter: drop-shadow(0 0 10px rgba(248, 250, 252, 0.3)); }
            100% { filter: drop-shadow(0 0 20px rgba(248, 250, 252, 0.5)); }
        }
        .tone-row {
            display: flex;
            gap: 16px;
            margin-bottom: 32px;
            width: 100%;
            justify-content: center;
            flex-wrap: wrap;
        }
        .tone-btn {
            background: rgba(30, 41, 59, 0.6);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(71, 85, 105, 0.3);
            color: #cbd5e1;
            font-weight: 500;
            border-radius: 12px;
            padding: 14px 28px;
            cursor: pointer;
            font-size: 0.95rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            outline: none;
            position: relative;
            overflow: hidden;
        }

        .tone-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .tone-btn:hover::before {
            left: 100%;
        }

        .tone-btn.active, .tone-btn:focus {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(99, 102, 241, 0.8));
            border-color: rgba(59, 130, 246, 0.6);
            color: #ffffff;
            box-shadow:
                0 8px 25px -8px rgba(59, 130, 246, 0.4),
                0 0 0 1px rgba(59, 130, 246, 0.2);
            transform: translateY(-2px);
        }

        .tone-btn:hover {
            background: rgba(51, 65, 85, 0.8);
            border-color: rgba(100, 116, 139, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 4px 20px -4px rgba(0, 0, 0, 0.3);
        }
        .input-area {
            width: 100%;
            margin-bottom: 32px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        textarea {
            width: 100%;
            min-height: 160px;
            font-size: 1rem;
            margin-bottom: 20px;
            border-radius: 16px;
            border: 1px solid rgba(71, 85, 105, 0.3);
            padding: 20px;
            background: rgba(15, 23, 42, 0.6);
            backdrop-filter: blur(10px);
            color: #e2e8f0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            resize: vertical;
            font-family: inherit;
            line-height: 1.6;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        textarea:focus {
            border-color: rgba(59, 130, 246, 0.5);
            outline: none;
            background: rgba(15, 23, 42, 0.8);
            box-shadow:
                0 0 0 3px rgba(59, 130, 246, 0.1),
                0 8px 25px -8px rgba(59, 130, 246, 0.2),
                inset 0 2px 4px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        textarea::placeholder {
            color: #94a3b8;
            font-style: italic;
        }
        /* Enhanced button styles */
        .btn {
            background: rgba(59, 130, 246, 0.9);
            color: #ffffff;
            border: 1px solid rgba(59, 130, 246, 0.6);
            padding: 12px 32px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            outline: none;
            font-family: inherit;
        }
        
        .btn:hover {
            background: rgba(59, 130, 246, 1);
            transform: translateY(-1px);
            box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.25);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        .file-upload {
            width: 100%;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }
        .file-upload input[type="file"] {
            display: none;
        }
        .file-upload-btn {
            background: rgba(30, 41, 59, 0.6);
            backdrop-filter: blur(10px);
            color: #cbd5e1;
            border: 1px solid rgba(100, 116, 139, 0.3);
            padding: 12px 24px;
            border-radius: 12px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-family: inherit;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .file-upload-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .file-upload-btn:hover::before {
            left: 100%;
        }

        .file-upload-btn:hover, .file-upload-btn:focus {
            background: rgba(51, 65, 85, 0.8);
            border-color: rgba(148, 163, 184, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 4px 20px -4px rgba(0, 0, 0, 0.3);
        }
        #fileName {
            color: #94a3b8;
            font-size: 0.9rem;
        }
        #generateBtn {
            width: 100%;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.9), rgba(99, 102, 241, 0.9));
            color: #ffffff;
            border: 1px solid rgba(59, 130, 246, 0.4);
            padding: 18px 0;
            border-radius: 16px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 28px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            outline: none;
            font-family: inherit;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 8px 25px -8px rgba(59, 130, 246, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.1);
        }

        #generateBtn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        #generateBtn:hover::before {
            left: 100%;
        }

        #generateBtn:active {
            transform: translateY(1px);
        }

        #generateBtn:hover, #generateBtn:focus {
            background: linear-gradient(135deg, rgba(59, 130, 246, 1), rgba(99, 102, 241, 1));
            transform: translateY(-2px);
            box-shadow:
                0 12px 35px -8px rgba(59, 130, 246, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.2);
        }

        #generateBtn:disabled {
            background: rgba(71, 85, 105, 0.4);
            border-color: rgba(71, 85, 105, 0.2);
            color: #94a3b8;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        #generateBtn:disabled::before {
            display: none;
        }
        
        #status {
            color: #cbd5e1;
            margin-bottom: 16px;
            min-height: 24px;
            text-align: center;
            font-size: 0.95rem;
        }
        .audio-row {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
        }
        audio {
            width: 100%;
            border-radius: 16px;
            background: rgba(15, 23, 42, 0.8);
            outline: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow:
                0 8px 25px -8px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        audio:hover {
            transform: translateY(-1px);
            box-shadow:
                0 12px 35px -8px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
        }

        #downloadBtn {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.9), rgba(16, 185, 129, 0.9));
            color: #ffffff;
            border: 1px solid rgba(34, 197, 94, 0.4);
            padding: 12px 28px;
            border-radius: 12px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            outline: none;
            font-family: inherit;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 4px 14px 0 rgba(34, 197, 94, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.1);
        }

        #downloadBtn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        #downloadBtn:hover::before {
            left: 100%;
        }

        #downloadBtn:hover, #downloadBtn:focus {
            background: linear-gradient(135deg, rgba(34, 197, 94, 1), rgba(16, 185, 129, 1));
            transform: translateY(-2px);
            box-shadow:
                0 8px 25px -8px rgba(34, 197, 94, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.2);
        }

        #downloadBtn:active {
            transform: translateY(0);
        }
        
        @media (max-width: 768px) {
            .container { 
                padding: 32px 24px; 
                margin: 20px auto;
                width: 95%;
            }
            h2 { font-size: 2rem; }
            .tone-row { flex-direction: column; gap: 12px; }
            .tone-btn { width: 100%; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>EchoVerse</h2>
        <!-- Tone selection buttons -->
        <div class="tone-row" id="toneRow">
            <button class="tone-btn active" data-tone="neutral" id="toneNeutral">Neutral</button>
            <button class="tone-btn" data-tone="suspense" id="toneSuspense">Suspense</button>
            <button class="tone-btn" data-tone="motivational" id="toneMotivational">Motivational</button>
        </div>
        <!-- Input area: textarea or file upload -->
        <div class="input-area">
            <div class="file-upload">
                <label class="file-upload-btn" for="fileInput">Upload .txt</label>
                <input type="file" id="fileInput" accept=".txt">
                <span id="fileName"></span>
            </div>
            <textarea id="story" placeholder="Type or paste your story here..."></textarea>
        </div>
        <div id="status"></div>
        <button id="generateBtn">Generate</button>
        <div class="audio-row">
            <audio id="audioPlayer" controls style="display:none;"></audio>
            <button id="downloadBtn" style="display:none;">Download Audio</button>
        </div>
    </div>
    <script>
        // --- Tone selection logic with animation ---
        // Keeps track of the selected tone and updates button styles
        const toneRow = document.getElementById('toneRow');
        let selectedTone = 'neutral'; // Default

        toneRow.addEventListener('click', function(e) {
            if (e.target.classList.contains('tone-btn')) {
                // Remove 'active' from all buttons
                document.querySelectorAll('.tone-btn').forEach(btn => btn.classList.remove('active'));
                // Add 'active' to clicked button
                e.target.classList.add('active');
                selectedTone = e.target.getAttribute('data-tone');
            }
        });

        // --- File upload logic for .txt files ---
        const fileInput = document.getElementById('fileInput');
        const fileNameSpan = document.getElementById('fileName');
        const textarea = document.getElementById('story');

        // When a file is selected, read its contents into the textarea
        fileInput.addEventListener('change', function() {
            const file = fileInput.files[0];
            if (file && file.type === "text/plain") {
                fileNameSpan.textContent = file.name;
                const reader = new FileReader();
                reader.onload = function(e) {
                    textarea.value = e.target.result;
                };
                reader.readAsText(file);
            } else {
                fileNameSpan.textContent = '';
                textarea.value = '';
            }
        });


        
        // --- Generate button logic (AI narration logic untouched) ---
        document.getElementById('generateBtn').onclick = async function(e) {
            e.preventDefault();
            const btn = this;
            const audio = document.getElementById('audioPlayer');
            const status = document.getElementById('status');
            const downloadBtn = document.getElementById('downloadBtn');
            const text = textarea.value.trim();
            if (!text) {
                status.textContent = 'Please enter some text or upload a .txt file.';
                return;
            }
            btn.disabled = true;
            status.textContent = "Generating audio...";
            downloadBtn.style.display = "none";
            try {
                const formData = new FormData();
                formData.append('text', text);
                // Optionally send tone to backend in the future:
                // formData.append('tone', selectedTone);
                const response = await fetch('/speak', { method: 'POST', body: formData });
                const data = await response.json();
                if (data.success || data.audio_url) {
                    // Support both {success:true, file:...} and {audio_url:...}
                    const audioUrl = data.file || data.audio_url;
                    audio.src = audioUrl;
                    audio.style.display = '';
                    audio.load();
                    audio.play();
                    status.textContent = "Audio ready!";
                    textarea.value = "";
                    // Show download button and set its link
                    downloadBtn.style.display = '';
                    downloadBtn.onclick = function() {
                        // Download the audio file
                        const link = document.createElement('a');
                        link.href = audioUrl;
                        link.download = audioUrl.split('/').pop();
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                    };
                } else {
                    status.textContent = data.error || "Failed to generate audio.";
                }
            } catch (err) {
                status.textContent = "Error contacting server.";
            } finally {
                btn.disabled = false;
            }
        };

        // Focus textarea on load for fast typing
        textarea.focus();
    </script>
</body>
</html>