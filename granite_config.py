"""
IBM Granite Hybrid Text-to-Speech Configuration
Configure IBM Granite models for text processing + TTS synthesis
"""

import os
from typing import Dict, List, Optional

class GraniteConfig:
    """Configuration class for IBM Granite Hybrid TTS"""

    # IBM Granite Text Processing Models
    GRANITE_MODELS = {
        "text_processor": {
            "name": "ibm-granite/granite-3.3-8b-instruct",
            "description": "IBM Granite model for text enhancement and processing",
            "type": "text_generation",
            "supported_languages": ["en", "fr", "de", "es", "pt"],
        },
        "speech_model": {
            "name": "ibm-granite/granite-speech-3.3-8b",
            "description": "IBM Granite Speech model (ASR - for future integration)",
            "type": "speech_recognition",
            "supported_languages": ["en", "fr", "de", "es", "pt"],
        }
    }

    # TTS Backend Configuration
    TTS_BACKENDS = {
        "gtts": {
            "name": "Google Text-to-Speech",
            "description": "Online TTS service with high quality",
            "requires_internet": True,
            "supported_languages": ["en", "fr", "de", "es", "pt"],
            "audio_format": "mp3"
        },
        "pyttsx3": {
            "name": "pyttsx3 Offline TTS",
            "description": "Offline TTS engine",
            "requires_internet": False,
            "supported_languages": ["en"],
            "audio_format": "wav"
        }
    }
    
    # Tone Processing Configuration
    TONE_SETTINGS = {
        "neutral": {
            "description": "Standard professional narration",
            "processing": {
                "speed_factor": 1.0,
                "pitch_factor": 1.0,
                "emphasis": "normal"
            }
        },
        "suspense": {
            "description": "Dramatic, mysterious narration",
            "processing": {
                "speed_factor": 0.8,
                "pitch_factor": 0.9,
                "emphasis": "dramatic",
                "pauses": True
            }
        },
        "motivational": {
            "description": "Energetic, inspiring narration",
            "processing": {
                "speed_factor": 1.1,
                "pitch_factor": 1.1,
                "emphasis": "energetic",
                "exclamation": True
            }
        }
    }
    
    # Audio Output Configuration
    AUDIO_CONFIG = {
        "format": "wav",
        "sample_rate": 22050,
        "channels": 1,
        "bit_depth": 16,
        "quality": "high"
    }
    
    # Performance Configuration
    PERFORMANCE_CONFIG = {
        "use_gpu": True,
        "batch_size": 1,
        "max_length": 1000,  # Maximum characters per synthesis
        "cache_models": True,
        "optimize_memory": True
    }
    
    # IBM Cloud Configuration (if using IBM Watson TTS)
    IBM_CLOUD_CONFIG = {
        "api_key": os.getenv("IBM_WATSON_API_KEY"),
        "service_url": os.getenv("IBM_WATSON_SERVICE_URL"),
        "version": "2021-06-01"
    }
    
    @classmethod
    def get_model_config(cls, model_type: str = "primary") -> Dict:
        """Get configuration for specified model type"""
        return cls.GRANITE_MODELS.get(model_type, cls.GRANITE_MODELS["primary"])
    
    @classmethod
    def get_tone_config(cls, tone: str = "neutral") -> Dict:
        """Get configuration for specified tone"""
        return cls.TONE_SETTINGS.get(tone, cls.TONE_SETTINGS["neutral"])
    
    @classmethod
    def get_supported_tones(cls) -> List[str]:
        """Get list of supported tones"""
        return list(cls.TONE_SETTINGS.keys())
    
    @classmethod
    def validate_config(cls) -> bool:
        """Validate the configuration"""
        try:
            # Check if required models are accessible
            for model_key, model_config in cls.GRANITE_MODELS.items():
                print(f"✓ Model {model_key}: {model_config['name']}")
            
            # Check tone configurations
            for tone_key in cls.TONE_SETTINGS.keys():
                print(f"✓ Tone {tone_key}: configured")
            
            print("✅ Granite configuration validated successfully")
            return True
            
        except Exception as e:
            print(f"❌ Configuration validation failed: {e}")
            return False

# Environment-specific settings
class DevelopmentConfig(GraniteConfig):
    """Development environment configuration"""
    DEBUG = True
    PERFORMANCE_CONFIG = {
        **GraniteConfig.PERFORMANCE_CONFIG,
        "use_gpu": False,  # Use CPU for development
        "cache_models": False
    }

class ProductionConfig(GraniteConfig):
    """Production environment configuration"""
    DEBUG = False
    PERFORMANCE_CONFIG = {
        **GraniteConfig.PERFORMANCE_CONFIG,
        "use_gpu": True,
        "cache_models": True,
        "optimize_memory": True
    }

# Select configuration based on environment
config_env = os.getenv("FLASK_ENV", "development")
if config_env == "production":
    Config = ProductionConfig
else:
    Config = DevelopmentConfig

# Export the active configuration
__all__ = ["Config", "GraniteConfig", "DevelopmentConfig", "ProductionConfig"]
