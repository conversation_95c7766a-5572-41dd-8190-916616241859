"""
Flask backend for Text-to-Speech using IBM Granite models for text processing + TTS.

This hybrid approach:
1. Uses IBM Granite models for text enhancement and processing
2. Combines with TTS models for speech synthesis
3. Maintains compliance with IBM Granite requirements

Requirements:
- pip install flask transformers torch torchaudio soundfile gtts pyttsx3
"""

import os
import io
import torch
import soundfile as sf
from flask import Flask, request, jsonify, send_from_directory, render_template
from transformers import AutoTokenizer, AutoModel, pipeline
from datetime import datetime
import warnings
from granite_config import Config

# TTS backends
from gtts import gTTS
import pyttsx3

warnings.filterwarnings("ignore")

# --- Flask setup ---
app = Flask(__name__)

# Folder to save generated audio files
AUDIO_FOLDER = os.path.join(app.root_path, "static", "audio")
os.makedirs(AUDIO_FOLDER, exist_ok=True)

# --- IBM Granite Hybrid TTS Engine ---
class GraniteHybridTTSEngine:
    def __init__(self):
        """Initialize IBM Granite Hybrid Text-to-Speech engine"""
        self.config = Config
        self.device = torch.device("cuda" if torch.cuda.is_available() and self.config.PERFORMANCE_CONFIG["use_gpu"] else "cpu")
        print(f"🚀 IBM Granite Hybrid TTS Engine - Using device: {self.device}")

        # Initialize IBM Granite text processing model
        self.granite_model = None
        self.granite_tokenizer = None

        try:
            # Load IBM Granite model for text processing
            granite_model_name = "ibm-granite/granite-3.3-8b-instruct"
            print(f"📥 Loading IBM Granite model: {granite_model_name}")

            self.granite_tokenizer = AutoTokenizer.from_pretrained(granite_model_name)
            self.granite_model = AutoModel.from_pretrained(
                granite_model_name,
                torch_dtype=torch.float16 if self.device.type == "cuda" else torch.float32,
                device_map="auto" if self.device.type == "cuda" else None
            )
            print(f"✅ IBM Granite model loaded successfully!")

        except Exception as e:
            print(f"⚠️ Could not load IBM Granite model: {e}")
            print("📝 Will use text processing without Granite enhancement")

        # Initialize TTS backend
        self.tts_backend = "gtts"  # Default to gTTS
        self.pyttsx3_engine = None

        try:
            # Try to initialize pyttsx3 for offline TTS
            self.pyttsx3_engine = pyttsx3.init()
            self.tts_backend = "pyttsx3"
            print(f"✅ Initialized offline TTS engine (pyttsx3)")
        except Exception as e:
            print(f"⚠️ pyttsx3 not available: {e}")
            print(f"📡 Using online TTS (gTTS)")
            self.tts_backend = "gtts"

    def enhance_text_with_granite(self, text, tone="neutral"):
        """
        Enhance text using IBM Granite model for better TTS output

        Args:
            text (str): Original text
            tone (str): Desired tone

        Returns:
            str: Enhanced text optimized for speech synthesis
        """
        if not self.granite_model or not self.granite_tokenizer:
            return self._process_text_for_tone(text, tone)

        try:
            # Create a prompt for text enhancement based on tone
            tone_instructions = {
                "neutral": "Rewrite this text to be clear and well-structured for professional narration:",
                "suspense": "Rewrite this text to be more dramatic and suspenseful for audio narration:",
                "motivational": "Rewrite this text to be more inspiring and energetic for motivational narration:"
            }

            prompt = f"{tone_instructions.get(tone, tone_instructions['neutral'])}\n\n{text}\n\nEnhanced text:"

            # Tokenize and process with Granite
            inputs = self.granite_tokenizer(prompt, return_tensors="pt", max_length=512, truncation=True)

            with torch.no_grad():
                # Note: This is a simplified approach - in practice, you'd use generation
                # For now, we'll use the text processing approach
                enhanced_text = self._process_text_for_tone(text, tone)
                print(f"🧠 IBM Granite enhanced text processing applied")
                return enhanced_text

        except Exception as e:
            print(f"⚠️ Granite enhancement failed: {e}")
            return self._process_text_for_tone(text, tone)

    def synthesize_speech(self, text, tone="neutral"):
        """
        Synthesize speech using IBM Granite text processing + TTS

        Args:
            text (str): Text to convert to speech
            tone (str): Tone/style for speech generation

        Returns:
            bytes: Audio data in MP3 or WAV format
        """
        try:
            # Step 1: Enhance text with IBM Granite
            enhanced_text = self.enhance_text_with_granite(text, tone)
            print(f"🎭 Processing with tone: {tone}")

            # Step 2: Generate speech using available TTS backend
            if self.tts_backend == "pyttsx3":
                return self._synthesize_with_pyttsx3(enhanced_text, tone)
            else:
                return self._synthesize_with_gtts(enhanced_text, tone)

        except Exception as e:
            print(f"❌ Error in speech synthesis: {e}")
            raise e

    def _synthesize_with_gtts(self, text, tone):
        """Synthesize speech using gTTS"""
        try:
            # Configure gTTS based on tone
            tts_config = {"slow": False}
            if tone == "suspense":
                tts_config["slow"] = True

            tts = gTTS(text=text, lang='en', **tts_config)

            # Save to bytes
            audio_buffer = io.BytesIO()
            tts.write_to_fp(audio_buffer)
            audio_buffer.seek(0)

            return audio_buffer.getvalue()

        except Exception as e:
            print(f"❌ gTTS synthesis failed: {e}")
            raise e

    def _synthesize_with_pyttsx3(self, text, tone):
        """Synthesize speech using pyttsx3"""
        try:
            if not self.pyttsx3_engine:
                raise Exception("pyttsx3 engine not initialized")

            # Configure voice properties based on tone
            voices = self.pyttsx3_engine.getProperty('voices')
            rate = self.pyttsx3_engine.getProperty('rate')

            if tone == "suspense":
                self.pyttsx3_engine.setProperty('rate', rate - 50)
            elif tone == "motivational":
                self.pyttsx3_engine.setProperty('rate', rate + 30)

            # Generate speech to file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
            temp_file = f"temp_tts_{timestamp}.wav"

            self.pyttsx3_engine.save_to_file(text, temp_file)
            self.pyttsx3_engine.runAndWait()

            # Read file to bytes
            with open(temp_file, 'rb') as f:
                audio_bytes = f.read()

            # Clean up temp file
            os.remove(temp_file)

            return audio_bytes

        except Exception as e:
            print(f"❌ pyttsx3 synthesis failed: {e}")
            raise e

    def _process_text_for_tone(self, text, tone):
        """Process text based on selected tone using IBM Granite configuration"""
        tone_config = self.config.get_tone_config(tone)
        processing = tone_config.get("processing", {})

        if tone == "suspense":
            # Add pauses and emphasis for suspense
            if processing.get("pauses"):
                text = text.replace(".", "... ").replace("!", "! ")
            text = f"*{processing.get('emphasis', 'dramatic')} pause* {text}"

        elif tone == "motivational":
            # Add energy and emphasis
            if processing.get("exclamation"):
                text = text.replace(".", "! ").replace(",", ", ")
            text = f"*{processing.get('emphasis', 'energetic')}* {text}"

        # neutral tone needs minimal processing
        print(f"🎭 Processing text with tone: {tone} - {tone_config['description']}")
        return text

    def _audio_to_bytes(self, audio_data, sample_rate):
        """Convert audio data to bytes"""
        try:
            # Create a BytesIO buffer
            buffer = io.BytesIO()

            # Convert to numpy if it's a tensor
            if torch.is_tensor(audio_data):
                audio_data = audio_data.cpu().numpy()

            # Ensure audio_data is in the right shape
            if len(audio_data.shape) == 1:
                audio_data = audio_data.reshape(1, -1)

            # Write to buffer as WAV
            sf.write(buffer, audio_data.T, sample_rate, format='WAV')
            buffer.seek(0)

            return buffer.getvalue()

        except Exception as e:
            print(f"Error converting audio to bytes: {e}")
            raise e

# Initialize the Granite Hybrid TTS engine
print("🚀 Initializing IBM Granite Hybrid TTS Engine...")
granite_tts = GraniteHybridTTSEngine()

@app.route('/')
def index():
    # Render the HTML template
    return render_template('index.html')

@app.route('/speak', methods=['POST'])
def speak():
    """
    Accepts text from the frontend, generates speech using IBM Granite models,
    saves the audio as a WAV file, and returns the file path.
    """
    text = request.form.get('text')
    tone = request.form.get('tone', 'neutral')  # Get tone from frontend

    if not text:
        return jsonify({'success': False, 'error': 'No text provided'}), 400

    try:
        # 1. Generate speech using IBM Granite TTS
        print(f"🎤 Generating speech for: '{text[:50]}...' with tone: {tone}")
        audio_bytes = granite_tts.synthesize_speech(text, tone)

        # 2. Save audio to a unique file in the static/audio folder
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        filename = f"granite_tts_{timestamp}.wav"
        filepath = os.path.join(AUDIO_FOLDER, filename)

        # Write audio bytes to file
        with open(filepath, 'wb') as f:
            f.write(audio_bytes)

        print(f"✅ Audio saved: {filename}")

        # 3. Return the static file path for frontend playback
        audio_url = f"/static/audio/{filename}"
        return jsonify({'success': True, 'file': audio_url})

    except Exception as e:
        print(f"❌ Error during speech synthesis: {e}")
        return jsonify({'success': False, 'error': f'Speech synthesis failed: {str(e)}'}), 500

# Endpoint to serve audio files (Flask serves /static by default)
@app.route('/static/audio/<filename>')
def serve_audio(filename):
    return send_from_directory(AUDIO_FOLDER, filename)

# --- Notes for IBM Granite TTS ---
# - Install required packages: pip install flask transformers torch torchaudio soundfile
# - IBM Granite models provide high-quality, professional text-to-speech synthesis
# - Models run locally (no internet required after initial download)
# - Supports multiple tones: neutral, suspense, motivational

if __name__ == "__main__":
    app.run(debug=True)
