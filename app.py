"""
Flask backend for Text-to-Speech using IBM Granite models.

Requirements:
- pip install flask ibm-watson transformers torch torchaudio soundfile
"""

import os
import io
import torch
import torchaudio
import soundfile as sf
from flask import Flask, request, jsonify, send_from_directory, render_template
from transformers import <PERSON>Tokenizer, AutoModel, pipeline
from datetime import datetime
import warnings
from granite_config import Config
warnings.filterwarnings("ignore")

# --- Flask setup ---
app = Flask(__name__)

# Folder to save generated audio files
AUDIO_FOLDER = os.path.join(app.root_path, "static", "audio")
os.makedirs(AUDIO_FOLDER, exist_ok=True)

# --- IBM Granite TTS Configuration ---
class GraniteTTSEngine:
    def __init__(self):
        """Initialize IBM Granite Text-to-Speech engine"""
        self.config = Config
        self.device = torch.device("cuda" if torch.cuda.is_available() and self.config.PERFORMANCE_CONFIG["use_gpu"] else "cpu")
        print(f"🚀 IBM Granite TTS Engine - Using device: {self.device}")

        # Initialize with IBM Granite models from configuration
        try:
            # Primary: Try to load IBM Granite TTS model
            primary_model = self.config.get_model_config("primary")
            self.model_name = primary_model["name"]
            self.tts_pipeline = pipeline(
                "text-to-speech",
                model=self.model_name,
                device=0 if self.device.type == "cuda" else -1
            )
            print(f"✅ Loaded IBM Granite TTS model: {self.model_name}")
            print(f"📋 Model description: {primary_model['description']}")

        except Exception as e:
            print(f"⚠️ Error loading primary IBM Granite model: {e}")
            # Fallback to secondary model
            fallback_model = self.config.get_model_config("fallback")
            try:
                self.model_name = fallback_model["name"]
                self.tts_pipeline = pipeline(
                    "text-to-speech",
                    model=self.model_name,
                    device=0 if self.device.type == "cuda" else -1
                )
                print(f"✅ Loaded fallback TTS model: {self.model_name}")
                print(f"📋 Model description: {fallback_model['description']}")
            except Exception as e2:
                print(f"❌ Failed to load any IBM Granite-compatible TTS model: {e2}")
                self.tts_pipeline = None

    def synthesize_speech(self, text, tone="neutral"):
        """
        Synthesize speech from text using IBM Granite-compatible models

        Args:
            text (str): Text to convert to speech
            tone (str): Tone/style for speech generation

        Returns:
            bytes: Audio data in WAV format
        """
        if not self.tts_pipeline:
            raise Exception("TTS model not loaded")

        try:
            # Adjust text based on tone for better expression
            processed_text = self._process_text_for_tone(text, tone)

            # Generate speech
            result = self.tts_pipeline(processed_text)

            # Convert to audio bytes
            if isinstance(result, dict) and 'audio' in result:
                audio_data = result['audio']
                sample_rate = result.get('sampling_rate', 22050)
            else:
                audio_data = result
                sample_rate = 22050

            # Convert to bytes
            audio_bytes = self._audio_to_bytes(audio_data, sample_rate)
            return audio_bytes

        except Exception as e:
            print(f"Error in speech synthesis: {e}")
            raise e

    def _process_text_for_tone(self, text, tone):
        """Process text based on selected tone using IBM Granite configuration"""
        tone_config = self.config.get_tone_config(tone)
        processing = tone_config.get("processing", {})

        if tone == "suspense":
            # Add pauses and emphasis for suspense
            if processing.get("pauses"):
                text = text.replace(".", "... ").replace("!", "! ")
            text = f"*{processing.get('emphasis', 'dramatic')} pause* {text}"

        elif tone == "motivational":
            # Add energy and emphasis
            if processing.get("exclamation"):
                text = text.replace(".", "! ").replace(",", ", ")
            text = f"*{processing.get('emphasis', 'energetic')}* {text}"

        # neutral tone needs minimal processing
        print(f"🎭 Processing text with tone: {tone} - {tone_config['description']}")
        return text

    def _audio_to_bytes(self, audio_data, sample_rate):
        """Convert audio data to bytes"""
        try:
            # Create a BytesIO buffer
            buffer = io.BytesIO()

            # Convert to numpy if it's a tensor
            if torch.is_tensor(audio_data):
                audio_data = audio_data.cpu().numpy()

            # Ensure audio_data is in the right shape
            if len(audio_data.shape) == 1:
                audio_data = audio_data.reshape(1, -1)

            # Write to buffer as WAV
            sf.write(buffer, audio_data.T, sample_rate, format='WAV')
            buffer.seek(0)

            return buffer.getvalue()

        except Exception as e:
            print(f"Error converting audio to bytes: {e}")
            raise e

# Initialize the Granite TTS engine
print("🚀 Initializing IBM Granite TTS Engine...")
granite_tts = GraniteTTSEngine()

@app.route('/')
def index():
    # Render the HTML template
    return render_template('index.html')

@app.route('/speak', methods=['POST'])
def speak():
    """
    Accepts text from the frontend, generates speech using IBM Granite models,
    saves the audio as a WAV file, and returns the file path.
    """
    text = request.form.get('text')
    tone = request.form.get('tone', 'neutral')  # Get tone from frontend

    if not text:
        return jsonify({'success': False, 'error': 'No text provided'}), 400

    try:
        # 1. Generate speech using IBM Granite TTS
        print(f"🎤 Generating speech for: '{text[:50]}...' with tone: {tone}")
        audio_bytes = granite_tts.synthesize_speech(text, tone)

        # 2. Save audio to a unique file in the static/audio folder
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        filename = f"granite_tts_{timestamp}.wav"
        filepath = os.path.join(AUDIO_FOLDER, filename)

        # Write audio bytes to file
        with open(filepath, 'wb') as f:
            f.write(audio_bytes)

        print(f"✅ Audio saved: {filename}")

        # 3. Return the static file path for frontend playback
        audio_url = f"/static/audio/{filename}"
        return jsonify({'success': True, 'file': audio_url})

    except Exception as e:
        print(f"❌ Error during speech synthesis: {e}")
        return jsonify({'success': False, 'error': f'Speech synthesis failed: {str(e)}'}), 500

# Endpoint to serve audio files (Flask serves /static by default)
@app.route('/static/audio/<filename>')
def serve_audio(filename):
    return send_from_directory(AUDIO_FOLDER, filename)

# --- Notes for IBM Granite TTS ---
# - Install required packages: pip install flask transformers torch torchaudio soundfile
# - IBM Granite models provide high-quality, professional text-to-speech synthesis
# - Models run locally (no internet required after initial download)
# - Supports multiple tones: neutral, suspense, motivational

if __name__ == "__main__":
    app.run(debug=True)
